// Logger utility - disabled for production
const Logger = {
  info: (message: string, data: any = null) => {
    // Production mode - logging disabled
  },
  success: (message: string, data: any = null) => {
    // Production mode - logging disabled
  },
  error: (message: string, error: any = null) => {
    // Keep error logging for critical issues
    console.error(`[Formify Error] ${message}`, error ? error : '');
  },
  api: {
    request: (provider: string, endpoint: string, data: any) => {
      // Production mode - logging disabled
    },
    response: (provider: string, response: any) => {
      // Production mode - logging disabled
    }
  }
};

export abstract class AIService {
  protected apiKey: string;
  protected model: string | null;

  constructor(apiKey: string, model: string | null = null) {
    this.apiKey = apiKey;
    this.model = model;
  }

  abstract generateContent(prompt: string): Promise<string>;
}

export class OpenAIService extends AIService {
  constructor(apiKey: string, model: string | null = 'gpt-3.5-turbo') {
    super(apiKey, model);
  }

  async generateContent(prompt: string): Promise<string> {
    try {
      Logger.info('Generating content with OpenAI', { model: this.model });

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            { role: 'user', content: prompt }
          ],
          temperature: 0.7
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      Logger.success('Successfully generated content with OpenAI');

      return data.choices[0].message.content;
    } catch (error) {
      Logger.error('Error generating content with OpenAI', error);
      throw error;
    }
  }
}

export class ClaudeService extends AIService {
  constructor(apiKey: string, model: string | null = 'claude-3-sonnet-20240229') {
    super(apiKey, model);
  }

  async generateContent(prompt: string): Promise<string> {
    try {
      Logger.info('Generating content with Claude', { model: this.model });

      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            { role: 'user', content: prompt }
          ],
          max_tokens: 1000
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      Logger.success('Successfully generated content with Claude');

      return data.content[0].text;
    } catch (error) {
      Logger.error('Error generating content with Claude', error);
      throw error;
    }
  }
}

export class MoonshotService extends AIService {
  constructor(apiKey: string, model: string | null = 'moonshot-v1-32k') {
    super(apiKey, model);
  }

  async generateContent(prompt: string): Promise<string> {
    try {
      Logger.info('Generating content with Moonshot', { model: this.model });

      const response = await fetch('https://api.moonshot.cn/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            { role: 'user', content: prompt }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      Logger.success('Successfully generated content with Moonshot');

      return data.choices[0].message.content;
    } catch (error) {
      Logger.error('Error generating content with Moonshot', error);
      throw error;
    }
  }
}

export class GeminiService extends AIService {
  constructor(apiKey: string, model: string | null = 'gemini-2.0-flash') {
    super(apiKey, model);
  }

  async generateContent(prompt: string): Promise<string> {
    try {
      Logger.info('Generating content with Gemini', { model: this.model });

      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contents: [
            { role: 'user', parts: [{ text: prompt }] }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      Logger.success('Successfully generated content with Gemini');

      return data.candidates[0].content.parts[0].text;
    } catch (error) {
      Logger.error('Error generating content with Gemini', error);
      throw error;
    }
  }
}

export class DeepSeekService extends AIService {
  constructor(apiKey: string, model: string | null = 'deepseek-chat') {
    super(apiKey, model);
  }

  async generateContent(prompt: string): Promise<string> {
    try {
      Logger.info('Generating content with DeepSeek', { model: this.model });

      const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            { role: 'user', content: prompt }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      Logger.success('Successfully generated content with DeepSeek');

      return data.choices[0].message.content;
    } catch (error) {
      Logger.error('Error generating content with DeepSeek', error);
      throw error;
    }
  }
}

// 定义模型接口
export interface Model {
  id: string;
  name: string;
  isFree?: boolean;
}

// 获取OpenAI模型列表
export async function fetchOpenAIModels(apiKey: string): Promise<Model[]> {
  try {
    Logger.info('Fetching OpenAI models');

    const response = await fetch('https://api.openai.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    Logger.success('Successfully fetched OpenAI models');

    // 过滤出GPT模型并格式化
    const gptModels = data.data
      .filter((model: any) => {
        const id = model.id.toLowerCase();
        return id.includes('gpt') && !id.includes('instruct');
      })
      .map((model: any) => ({
        id: model.id,
        name: formatModelName(model.id),
        isFree: model.id.includes('gpt-3.5')
      }));

    // 按照模型名称排序，GPT-4优先
    gptModels.sort((a: Model, b: Model) => {
      if (a.id.includes('gpt-4') && !b.id.includes('gpt-4')) return -1;
      if (!a.id.includes('gpt-4') && b.id.includes('gpt-4')) return 1;
      return a.name.localeCompare(b.name);
    });

    return gptModels;
  } catch (error) {
    Logger.error('Error fetching OpenAI models', error);
    // 返回默认模型列表
    return [
      { id: 'gpt-4', name: 'GPT-4' },
      { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', isFree: true }
    ];
  }
}

// 获取Claude模型列表
export async function fetchClaudeModels(apiKey: string): Promise<Model[]> {
  try {
    Logger.info('Fetching Claude models');

    const response = await fetch('https://api.anthropic.com/v1/models', {
      method: 'GET',
      headers: {
        'x-api-key': apiKey,
        'anthropic-version': '2023-01-01',
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    Logger.success('Successfully fetched Claude models');
    console.log('Claude API response:', JSON.stringify(data, null, 2));
    Logger.info('Claude API response:', data);

    // 检查响应格式
    if (!data.data || !Array.isArray(data.data)) {
      throw new Error('Unexpected response format from Claude API');
    }

    // 格式化模型数据
    const claudeModels = data.data.map((model: any) => ({
      id: model.id,
      name: model.display_name || formatModelName(model.id),
      isFree: false
    }));

    // 按照模型名称排序，Claude-3优先
    claudeModels.sort((a: Model, b: Model) => {
      if (a.id.includes('claude-3') && !b.id.includes('claude-3')) return -1;
      if (!a.id.includes('claude-3') && b.id.includes('claude-3')) return 1;
      return a.name.localeCompare(b.name);
    });

    return claudeModels;
  } catch (error) {
    Logger.error('Error fetching Claude models', error);
    // 返回默认模型列表
    return [
      { id: 'claude-3-opus-20240229', name: 'Claude-3 Opus' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude-3 Sonnet' },
      { id: 'claude-3-haiku-20240307', name: 'Claude-3 Haiku' },
      { id: 'claude-2.1', name: 'Claude-2.1' }
    ];
  }
}

// 获取Moonshot模型列表
export async function fetchMoonshotModels(apiKey: string): Promise<Model[]> {
  try {
    Logger.info('Fetching Moonshot models');

    const response = await fetch('https://api.moonshot.cn/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    Logger.success('Successfully fetched Moonshot models');

    // 格式化模型数据
    const moonshotModels = data.data.map((model: any) => ({
      id: model.id,
      name: formatModelName(model.id),
      isFree: false
    }));

    // 按照模型名称排序
    moonshotModels.sort((a: Model, b: Model) => a.name.localeCompare(b.name));

    return moonshotModels;
  } catch (error) {
    Logger.error('Error fetching Moonshot models', error);
    // 返回默认模型列表
    return [
      { id: 'moonshot-v1-8k', name: 'Moonshot V1 (8K)' },
      { id: 'moonshot-v1-32k', name: 'Moonshot V1 (32K)' },
      { id: 'moonshot-v1-128k', name: 'Moonshot V1 (128K)' }
    ];
  }
}

// 获取Gemini模型列表
export async function fetchGeminiModels(apiKey: string): Promise<Model[]> {
  try {
    Logger.info('Fetching Gemini models');

    const response = await fetch(`https://generativelanguage.googleapis.com/v1/models?key=${apiKey}`, {
      method: 'GET'
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    Logger.success('Successfully fetched Gemini models');

    // 过滤出Gemini模型并格式化
    const geminiModels = data.models
      .filter((model: any) => model.name.includes('gemini'))
      .map((model: any) => {
        const id = model.name.split('/').pop();
        return {
          id: id,
          name: formatModelName(id),
          isFree: id.includes('flash-lite') || id.includes('flash')
        };
      });

    // 按照模型名称排序
    geminiModels.sort((a: Model, b: Model) => {
      if (a.id.includes('2.0') && !b.id.includes('2.0')) return -1;
      if (!a.id.includes('2.0') && b.id.includes('2.0')) return 1;
      return a.name.localeCompare(b.name);
    });

    return geminiModels;
  } catch (error) {
    Logger.error('Error fetching Gemini models', error);
    // 返回默认模型列表
    return [
      { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash', isFree: true },
      { id: 'gemini-2.0-flash-lite', name: 'Gemini 2.0 Flash Lite', isFree: true },
      { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', isFree: true },
      { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' }
    ];
  }
}

// 获取DeepSeek模型列表
export async function fetchDeepSeekModels(apiKey: string): Promise<Model[]> {
  try {
    Logger.info('Fetching DeepSeek models');

    const response = await fetch('https://api.deepseek.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    Logger.success('Successfully fetched DeepSeek models');

    // 格式化模型数据
    const deepseekModels = data.data.map((model: any) => ({
      id: model.id,
      name: formatModelName(model.id),
      isFree: false
    }));

    // 按照模型名称排序
    deepseekModels.sort((a: Model, b: Model) => a.name.localeCompare(b.name));

    return deepseekModels;
  } catch (error) {
    Logger.error('Error fetching DeepSeek models', error);
    // 返回默认模型列表
    return [
      { id: 'deepseek-chat', name: 'DeepSeek Chat' },
      { id: 'deepseek-reasoner', name: 'DeepSeek Reasoner' }
    ];
  }
}

// 获取OpenRouter模型列表
export async function fetchOpenRouterModels(apiKey: string): Promise<Model[]> {
  try {
    Logger.info('Fetching OpenRouter models');

    const response = await fetch('https://openrouter.ai/api/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'https://fillify.tech',
        'X-Title': 'Fillify'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    Logger.success('Successfully fetched OpenRouter models');

    // 处理模型数据，转换为我们需要的格式
    const models = data.data.map((model: any) => {
      // 检查模型是否免费
      // 如果提示和完成的价格都是0，则认为是免费模型
      const promptPrice = parseFloat(model.pricing?.prompt || '0');
      const completionPrice = parseFloat(model.pricing?.completion || '0');
      const isFree = promptPrice === 0 && completionPrice === 0;

      // 移除所有提供商前缀（如 deepseek:, anthropic: 等）
      let displayName = model.name.replace(/^[\w-]+\/|^[\w-]+:/, '').trim();
      // 移除 (free) 标记，因为我们已经使用 isFree 属性和标签来显示
      displayName = displayName.replace(/\s*\(free\)\s*/i, '').trim();
      // 替换连字符为空格并美化名称
      displayName = displayName.replace(/-/g, ' ').trim();

      return {
        id: model.id,
        name: displayName,
        isFree: isFree
      };
    });

    // 按照模型名称排序，免费模型优先
    models.sort((a: Model, b: Model) => {
      if (a.isFree && !b.isFree) return -1;
      if (!a.isFree && b.isFree) return 1;
      return a.name.localeCompare(b.name);
    });

    return models;
  } catch (error) {
    Logger.error('Error fetching OpenRouter models', error);
    // 返回默认模型列表
    return [
      { id: 'openai/gpt-3.5-turbo', name: 'GPT-3.5 Turbo', isFree: false },
      { id: 'openai/gpt-4', name: 'GPT-4', isFree: false },
      { id: 'openai/gpt-4-turbo', name: 'GPT-4 Turbo', isFree: false },
      { id: 'anthropic/claude-3-opus', name: 'Claude-3 Opus', isFree: false },
      { id: 'anthropic/claude-3-sonnet', name: 'Claude-3 Sonnet', isFree: false },
      { id: 'meta-llama/llama-3-70b-instruct', name: 'Llama-3 70B', isFree: false },
      { id: 'mistralai/mistral-large', name: 'Mistral Large', isFree: false }
    ];
  }
}

// 获取Ollama模型列表
export async function fetchOllamaModels(endpoint: string = 'http://localhost:11434'): Promise<Model[]> {
  try {
    Logger.info('Fetching Ollama models', { endpoint });

    const response = await fetch(`${endpoint}/v1/models`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer ollama', // Ollama requires this header but doesn't use the key
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    Logger.success('Successfully fetched Ollama models');

    // 处理模型数据，转换为我们需要的格式
    const models = data.data.map((model: any) => ({
      id: model.id,
      name: formatModelName(model.id),
      isFree: true // Ollama 本地模型都是免费的
    }));

    // 按照模型名称排序
    models.sort((a: Model, b: Model) => a.name.localeCompare(b.name));

    return models;
  } catch (error) {
    Logger.error('Error fetching Ollama models', error);
    // 返回默认模型列表
    return [
      { id: 'llama2', name: 'Llama 2', isFree: true },
      { id: 'llama2:13b', name: 'Llama 2 13B', isFree: true },
      { id: 'llama2:70b', name: 'Llama 2 70B', isFree: true },
      { id: 'mistral', name: 'Mistral', isFree: true },
      { id: 'codellama', name: 'Code Llama', isFree: true },
      { id: 'phi', name: 'Phi', isFree: true }
    ];
  }
}

// 辅助函数：格式化模型名称
function formatModelName(modelId: string): string {
  // 移除版本号和日期
  let name = modelId.replace(/[-_]\d{8}$/, '');

  // 替换连字符和下划线为空格
  name = name.replace(/[-_]/g, ' ');

  // 处理特殊情况
  if (name.includes('gpt')) {
    name = name.replace(/gpt(\d)/, 'GPT-$1');
    name = name.replace('turbo', 'Turbo');
    name = name.replace('preview', 'Preview');
  } else if (name.includes('claude')) {
    name = name.replace(/claude(\d)/, 'Claude-$1');
    name = name.replace(/claude (\d)/, 'Claude-$1');
    name = name.replace(/\.1/, '.1');
    name = name.replace('opus', 'Opus');
    name = name.replace('sonnet', 'Sonnet');
  } else if (name.includes('gemini')) {
    name = name.replace('gemini', 'Gemini');
    name = name.replace('pro', 'Pro');
    name = name.replace('flash', 'Flash');
    name = name.replace('lite', 'Lite');
  } else if (name.includes('moonshot')) {
    name = name.replace('moonshot', 'Moonshot');
    name = name.replace('v1', 'V1');
    name = name.replace('8k', '(8K)');
    name = name.replace('32k', '(32K)');
    name = name.replace('128k', '(128K)');
  } else if (name.includes('deepseek')) {
    name = name.replace('deepseek', 'DeepSeek');
    name = name.replace('chat', 'Chat');
    name = name.replace('reasoner', 'Reasoner');
  } else if (name.includes('llama')) {
    name = name.replace(/llama(\d+)/, 'Llama $1');
    name = name.replace(/(\d+)b/g, '$1B');
    name = name.replace(/(\d+)k/g, '$1K');
    name = name.replace(/(\d+)m/g, '$1M');
  } else if (name.includes('mistral')) {
    name = name.replace('mistral', 'Mistral');
  } else if (name.includes('codellama')) {
    name = name.replace('codellama', 'Code Llama');
  } else if (name.includes('phi')) {
    name = name.replace('phi', 'Phi');
  }

  return name.trim();
}

export class OpenRouterService extends AIService {
  constructor(apiKey: string, model: string | null = 'openai/gpt-3.5-turbo') {
    super(apiKey, model);
  }

  async generateContent(prompt: string): Promise<string> {
    try {
      Logger.info('Generating content with OpenRouter', { model: this.model });

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'HTTP-Referer': 'https://fillify.tech', // 您的应用域名
          'X-Title': 'Fillify' // 您的应用名称
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            { role: 'user', content: prompt }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      Logger.success('Successfully generated content with OpenRouter');

      return data.choices[0].message.content;
    } catch (error) {
      Logger.error('Error generating content with OpenRouter', error);
      throw error;
    }
  }
}

export class OllamaService extends AIService {
  private endpoint: string;

  constructor(apiKey: string = 'ollama', model: string | null = 'llama2', endpoint: string = 'http://localhost:11434') {
    super(apiKey, model);
    this.endpoint = endpoint;
  }

  async generateContent(prompt: string): Promise<string> {
    try {
      Logger.info('Generating content with Ollama', { model: this.model, endpoint: this.endpoint });

      const response = await fetch(`${this.endpoint}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`, // Ollama requires this header but doesn't use the key
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            { role: 'user', content: prompt }
          ],
          stream: false
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      Logger.success('Successfully generated content with Ollama');

      return data.choices[0].message.content;
    } catch (error) {
      Logger.error('Error generating content with Ollama', error);
      throw error;
    }
  }
}

export class CustomProviderService extends AIService {
  private endpoint: string;

  constructor(apiKey: string, endpoint: string, model: string | null = null) {
    super(apiKey, model);
    this.endpoint = endpoint;
  }

  async generateContent(prompt: string): Promise<string> {
    try {
      Logger.info('Generating content with custom provider', { endpoint: this.endpoint });

      const response = await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            { role: 'user', content: prompt }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      Logger.success('Successfully generated content with custom provider');

      // 尝试处理不同的响应格式
      const content = data.choices?.[0]?.message?.content || // OpenAI 格式
                     data.response || // Claude 格式
                     data.output || // 通用格式
                     data.text || // 通用格式
                     JSON.stringify(data); // 如果都不匹配，返回原始响应

      return content;
    } catch (error) {
      Logger.error('Error generating content with custom provider', error);
      throw error;
    }
  }
}

export async function createAIService(provider: string, apiKey: string): Promise<AIService> {
  Logger.info(`Creating AI service for provider: ${provider}`);

  // Get the selected model from storage
  const storage = await chrome.storage.sync.get(['formify_settings']);
  const settings = storage.formify_settings || {};
  const selectedModel = settings.defaultModel || settings[`${provider}_model`];

  // 根据提供商创建相应的服务
  switch (provider.toLowerCase()) {
    case 'openai':
      return new OpenAIService(apiKey, selectedModel);
    case 'claude':
      return new ClaudeService(apiKey, selectedModel);
    case 'moonshot':
      return new MoonshotService(apiKey, selectedModel);
    case 'gemini':
      return new GeminiService(apiKey, selectedModel);
    case 'deepseek':
      return new DeepSeekService(apiKey, selectedModel);
    case 'openrouter':
      return new OpenRouterService(apiKey, selectedModel);
    case 'ollama':
      // 获取 Ollama 端点设置
      const ollamaEndpoint = settings.ollama_endpoint || 'http://localhost:11434';
      return new OllamaService('ollama', selectedModel, ollamaEndpoint);
  }

  // 如果是自定义提供商
  try {
    const storage = await chrome.storage.sync.get(['formify_custom_providers']);
    const customProviders = storage.formify_custom_providers || {};
    const customProvider = customProviders[provider];

    if (customProvider) {
      return new CustomProviderService(
        apiKey || customProvider.key,
        customProvider.endpoint,
        customProvider.model
      );
    }
  } catch (error) {
    Logger.error('Error loading custom provider', error);
  }

  // 如果都不匹配
  Logger.error(`Unsupported AI provider: ${provider}`);
  throw new Error(`Unsupported AI provider: ${provider}`);
}
