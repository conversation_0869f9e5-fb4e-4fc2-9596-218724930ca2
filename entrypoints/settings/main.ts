import { createApp } from 'vue'
import App from './App.vue'
import './settings.css'
import './modal.css'
import './button-styles.css'

const app = createApp(App)

// 创建一个全局属性，用于存储 URL 参数
const urlParams = new URLSearchParams(window.location.search)
const action = urlParams.get('action')
const tab = urlParams.get('tab')

app.config.globalProperties.$urlParams = {
  action,
  tab
}

// 挂载应用
const appInstance = app.mount('#app')

// 将应用实例暴露给 window 对象，以便在控制台中调试
window.__VUE_APP_INSTANCE__ = appInstance