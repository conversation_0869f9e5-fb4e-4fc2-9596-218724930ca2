import { StorageKeys } from './types'

export const STORAGE_KEYS: StorageKeys = {
  API_KEYS: 'formify_api_keys',
  SETTINGS: 'formify_settings',
  PROJECTS: 'formify_projects',
  TOKEN_STATS: 'formify_token_stats',
  CUSTOM_PROVIDERS: 'formify_custom_providers',
  FIRST_VISIT: 'formify_first_visit',
  COLLAPSED_SECTIONS: 'formify_collapsed_sections',
  ACTIVE_TAB: 'formify_active_tab',
  VALIDATED_KEYS: 'formify_validated_keys'
}

export const PROVIDER_MODELS = {
  openai: [
    { id: 'gpt-4', name: 'GPT-4' },
    { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo' },
    { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
  ],
  claude: [
    { id: 'claude-3-opus-20240229', name: 'Claude-<PERSON> <PERSON>' },
    { id: 'claude-3-sonnet-20240229', name: 'Claude-3 Sonnet' },
    { id: 'claude-2.1', name: 'Claude-2.1' }
  ],
  moonshot: [
    { id: 'moonshot-v1-8k', name: 'Moonshot V1 (8K)' },
    { id: 'moonshot-v1-32k', name: 'Moonshot V1 (32K)' },
    { id: 'moonshot-v1-128k', name: 'Moonshot V1 (128K)' }
  ],
  gemini: [
    { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash' },
    { id: 'gemini-2.0-flash-lite', name: 'Gemini 2.0 Flash Lite' },
    { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
    { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' }
  ],
  deepseek: [
    { id: 'deepseek-chat', name: 'DeepSeek Chat' },
    { id: 'deepseek-reasoner', name: 'DeepSeek Reasoner' }
  ],
  openrouter: [
    { id: 'openai/gpt-3.5-turbo', name: 'GPT-3.5 Turbo' },
    { id: 'openai/gpt-4', name: 'GPT-4' },
    { id: 'openai/gpt-4-turbo', name: 'GPT-4 Turbo' },
    { id: 'anthropic/claude-3-opus', name: 'Claude-3 Opus' },
    { id: 'anthropic/claude-3-sonnet', name: 'Claude-3 Sonnet' },
    { id: 'meta-llama/llama-3-70b-instruct', name: 'Llama-3 70B' },
    { id: 'mistralai/mistral-large', name: 'Mistral Large' }
  ],
  ollama: [
    { id: 'llama2', name: 'Llama 2' },
    { id: 'llama2:13b', name: 'Llama 2 13B' },
    { id: 'llama2:70b', name: 'Llama 2 70B' },
    { id: 'mistral', name: 'Mistral' },
    { id: 'codellama', name: 'Code Llama' },
    { id: 'phi', name: 'Phi' }
  ]
}