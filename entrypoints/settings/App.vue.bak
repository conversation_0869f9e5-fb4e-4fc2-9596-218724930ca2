<template>
  <div class="app-container">
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
    <!-- Sidebar Navigation -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <img src="/icon/icon48.png" alt="Fillify Logo" class="logo">
        <h1>Fillify Settings</h1>
      </div>
      <nav class="sidebar-nav">
        <button 
          class="nav-item" 
          :class="{ active: activeTab === 'general' }"
          @click="switchTab('general')"
          data-tab="general"
        >
          General
        </button>
        <button 
          class="nav-item" 
          :class="{ active: activeTab === 'library' }"
          @click="switchTab('library')"
          data-tab="library"
        >
          Library
        </button>
      </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <div class="content-wrapper">
        <!-- General Tab Content -->
        <div class="tab-content" :class="{ active: activeTab === 'general' }" id="general-tab">
          <!-- Quick Actions Card -->
          <div class="settings-card">
            <div class="card-header">
              <h2>Quick Actions</h2>
            </div>
            <div class="card-content">
              <div class="quick-actions">
                <div class="action-item">
                  <div class="action-info">
                    <h3>Onboarding Guide</h3>
                    <p>Review setup steps and quick tips</p>
                  </div>
                  <button id="show-onboarding" class="button-secondary" @click="showOnboarding">
                    Open Guide
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Token Usage Card -->
          <div class="settings-card" id="token-usage">
            <div class="card-header">
              <h2>Token Usage</h2>
            </div>
            <div class="card-content">
              <div class="token-grid">
                <!-- Token cards will be added dynamically -->
                <div v-for="(stats, provider) in tokenStats" :key="provider" class="token-card">
                  <div class="token-header">
                    <h3>{{ providerDisplayNames[provider] || provider }}</h3>
                  </div>
                  <div class="token-stats">
                    <div class="stat-item">
                      <span class="stat-label">Prompt Tokens</span>
                      <span class="stat-value">{{ stats.promptTokens?.toLocaleString() || 0 }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">Completion Tokens</span>
                      <span class="stat-value">{{ stats.completionTokens?.toLocaleString() || 0 }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">Total Tokens</span>
                      <span class="stat-value">{{ stats.totalTokens?.toLocaleString() || 0 }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">Last Updated</span>
                      <span class="stat-value">{{ formatDate(stats.lastUpdated) }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <button id="reset-stats" class="button-danger" @click="resetStats">
                Reset Statistics
              </button>
            </div>
          </div>

          <!-- AI Providers Card -->
          <div class="settings-card" id="ai-providers">
            <div class="card-header">
              <h2>AI Providers</h2>
            </div>
            <div class="card-content">
              <div class="providers-grid">
                <div v-for="provider in providers" :key="provider.id" class="provider-card">
                  <div class="provider-header">
                    <h3>{{ provider.name }}</h3>
                  </div>
                  <div class="key-input-group">
                    <input 
                      type="password" 
                      :id="`${provider.id}-key`"
                      class="key-input"
                      :class="{
                        'validating': validating[provider.id],
                        'valid': validatedKeys[provider.id],
                        'invalid': apiKeys[provider.id] && !validatedKeys[provider.id]
                      }"
                      :placeholder="`Enter ${provider.name} API key`"
                      v-model="apiKeys[provider.id]"
                      @input="debouncedValidateApiKey(provider.id)"
                    >
                    <div 
                      class="loading-spinner" 
                      :class="{ active: validating[provider.id] }"
                    ></div>
                    <button class="visibility-toggle" @click="toggleVisibility(provider.id)">
                      <svg viewBox="0 0 24 24" class="eye-icon" :class="{ 'show-password': !showPassword[provider.id] }">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <div class="provider-settings">
                <div class="settings-grid">
                  <!-- Default Provider Setting -->
                  <div class="setting-item">
                    <div class="setting-info">
                      <h4>Default Provider</h4>
                      <p>Select your preferred AI provider</p>
                    </div>
                    <select 
                      id="default-provider" 
                      class="select-input"
                      v-model="settings.defaultProvider"
                      @change="() => updateModelOptions()"
                    >
                      <option 
                        v-for="provider in providers" 
                        :key="provider.id"
                        :value="provider.id"
                        :disabled="!validatedKeys[provider.id]"
                      >
                        {{ provider.name }}
                      </option>
                    </select>
                  </div>

                  <!-- Default Model Setting -->
                  <div class="setting-item">
                    <div class="setting-info">
                      <h4>Default Model</h4>
                      <p>Select the default model for the chosen provider</p>
                    </div>
                    <select 
                      id="default-model" 
                      class="select-input"
                      v-model="settings.defaultModel"
                    >
                      <option 
                        v-for="model in availableModels" 
                        :key="model.id"
                        :value="model.id"
                      >
                        {{ model.name }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Library Tab Content -->
        <div class="tab-content" :class="{ active: activeTab === 'library' }" id="library-tab">
          <!-- Projects Card -->
          <div class="settings-card">
            <div class="card-header">
              <h2>Bug Report Projects</h2>
              <button class="button-secondary" @click="openProjectModal">
                <svg viewBox="0 0 24 24" class="nav-icon" style="margin-right: 0.5rem;">
                  <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
                </svg>
                Add Project
              </button>
            </div>
            <div class="card-content">
              <div class="setting-item">
                <div class="setting-info">
                  <h4>Show Project Details</h4>
                  <p>Display additional information for each project</p>
                </div>
                <label class="switch">
                  <input 
                    type="checkbox" 
                    id="show-project-details"
                    v-model="settings.showProjectDetails"
                    @change="() => saveSettings()"
                  >
                  <span class="slider"></span>
                </label>
              </div>
              
              <div class="projects-grid">
                <div 
                  v-for="project in visibleProjects" 
                  :key="project.id" 
                  class="project-card"
                >
                  <div class="project-header">
                    <h3>{{ project.name }}</h3>
                    <div class="project-actions">
                      <button class="button-icon" @click="editProject(project)">
                        <svg viewBox="0 0 24 24" class="nav-icon">
                          <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                      </button>
                      <button class="button-icon" @click="deleteProject(project.id)">
                        <svg viewBox="0 0 24 24" class="nav-icon">
                          <path d="M6 19c0 1.1.9 2 2 2H8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div v-if="settings.showProjectDetails" class="project-details">
                    <div class="detail-item">
                      <span class="detail-label">Description:</span>
                      <p>{{ project.description }}</p>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">Environment:</span>
                      <p>{{ project.environment }}</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div v-if="projects.length > (currentPage + 1) * projectsPerPage"
                class="load-more"
                @click="currentPage++"
              >
                加载更多
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- Project Modal -->
  <div id="project-modal" class="modal" :class="{ show: showProjectModal }">
    <div class="modal-content">
      <div class="modal-header">
        <h3>{{ editingProject ? 'Edit Project' : 'Add Project' }}</h3>
        <button class="modal-close" @click="closeProjectModal">&times;</button>
      </div>
      <div class="modal-body">
        <form class="modal-form" @submit.prevent="saveProject">
          <div class="form-group">
            <label for="project-name">Project Name</label>
            <input 
              type="text" 
              id="project-name" 
              v-model="projectForm.name"
              placeholder="Enter project name" 
              required
            >
          </div>
          <div class="form-group">
            <label for="project-description">Description</label>
            <textarea 
              id="project-description" 
              v-model="projectForm.description"
              placeholder="Enter project description"
            ></textarea>
          </div>
          <div class="form-group">
            <label for="project-environment">Environment</label>
            <textarea 
              id="project-environment" 
              v-model="projectForm.environment"
              placeholder="Enter environment information"
            ></textarea>
          </div>
          <div class="form-group">
            <label for="project-template">Bug Report Template</label>
            <textarea 
              id="project-template" 
              v-model="projectForm.template"
              placeholder="Enter bug report template"
            ></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="button-secondary" @click="closeProjectModal">Cancel</button>
        <button class="button-primary" @click="saveProject">Save Project</button>
      </div>
    </div>
  </div>

  <!-- Notification Component -->
  <div 
    v-if="notification.show" 
    class="notification"
    :class="notification.type"
    @click="hideNotification"
  >
    {{ notification.message }}
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { STORAGE_KEYS, PROVIDER_MODELS } from './constants'
import type { Provider, Model, Settings, Project, ProjectForm, Notification, TokenStats } from './types'
import { debounce, handleError } from './utils'

// 状态管理
const activeTab = ref('general')
const showProjectModal = ref(false)
const providers = ref<Provider[]>([
  { id: 'openai', name: 'OpenAI' },
  { id: 'claude', name: 'Claude' },
  { id: 'moonshot', name: 'Moonshot' },
  { id: 'gemini', name: 'Gemini' },
  { id: 'deepseek', name: 'DeepSeek' }
])
const apiKeys = reactive<Record<string, string>>({})
const validating = reactive<Record<string, boolean>>({})
const validatedKeys = reactive<Record<string, boolean>>({})
const showPassword = reactive<Record<string, boolean>>({})
const settings = reactive<Settings>({
  defaultProvider: 'openai',
  defaultModel: 'gpt-3.5-turbo',
  showProjectDetails: false
})
const tokenStats = reactive<Record<string, TokenStats>>({})
const projects = ref<Project[]>([])
const availableModels = ref<Model[]>([])

// 项目表单状态
const projectForm = reactive<ProjectForm>({
  name: '',
  description: '',
  environment: '',
  template: ''
})

const editingProject = ref<Project | null>(null)

// 添加通知状态
const notification = reactive<Notification>({
  show: false,
  message: '',
  type: 'success',
  timer: undefined
})

// 添加加载状态
const isLoading = ref(false)

// 添加项目列表虚拟滚动
const projectsPerPage = 10
const currentPage = ref(0)

const visibleProjects = computed(() => {
  const start = currentPage.value * projectsPerPage
  return projects.value.slice(start, start + projectsPerPage)
})

// 方法定义
const switchTab = (tab: string) => {
  activeTab.value = tab
  chrome.storage.sync.set({ [STORAGE_KEYS.ACTIVE_TAB]: tab })
}

const showOnboarding = () => {
  chrome.tabs.create({
    url: 'onboarding.html'
  })
}

const resetStats = async () => {
  if (!confirm('Are you sure you want to reset all token statistics? This action cannot be undone.')) {
    return
  }
  
  try {
    await chrome.storage.sync.set({ [STORAGE_KEYS.TOKEN_STATS]: {} })
    Object.keys(tokenStats).forEach(key => {
      tokenStats[key] = { 
        promptTokens: 0, 
        completionTokens: 0, 
        totalTokens: 0, 
        lastUpdated: new Date().toISOString() 
      }
    })
    showNotification('Statistics reset successfully', 'success')
  } catch (error) {
    handleError(error, 'Failed to reset statistics')
  }
}

// 修改 validateApiKey 中的验证逻辑
const validateApiKey = async (providerId: string) => {
  if (validating[providerId]) {
    return
  }
  
  validating[providerId] = true
  
  try {
    const key = apiKeys[providerId]
    if (!key) {
      // 清除该提供商的验证状态和保存的 key
      delete validatedKeys[providerId]
      const storage = await chrome.storage.sync.get([STORAGE_KEYS.API_KEYS, STORAGE_KEYS.VALIDATED_KEYS, STORAGE_KEYS.SETTINGS])
      const keys = storage[STORAGE_KEYS.API_KEYS] || {}
      const validated = storage[STORAGE_KEYS.VALIDATED_KEYS] || {}
      const settings = storage[STORAGE_KEYS.SETTINGS] || {}

      delete keys[providerId]
      delete validated[providerId]

      // 如果清除的是当前默认提供商，重置为第一个有效的提供商
      if (settings.defaultProvider === providerId) {
        const firstValidProvider = Object.keys(validated).find(key => validated[key])
        settings.defaultProvider = firstValidProvider || 'openai'
        updateModelOptions(false)
      }

      await chrome.storage.sync.set({
        [STORAGE_KEYS.API_KEYS]: keys,
        [STORAGE_KEYS.VALIDATED_KEYS]: validated,
        [STORAGE_KEYS.SETTINGS]: settings
      })
      validating[providerId] = false
      return
    }

    // 直接验证 API Key
    let endpoint = '';
    let testPayload = {};
    
    switch (providerId) {
      case 'openai':
        endpoint = 'https://api.openai.com/v1/chat/completions';
        testPayload = {
          model: "gpt-3.5-turbo",
          messages: [{ role: "user", content: "test" }],
          max_tokens: 1
        };
        break;
        
      case 'claude':
        endpoint = 'https://api.anthropic.com/v1/messages';
        testPayload = {
          model: "claude-3-sonnet-20240229",
          max_tokens: 1,
          messages: [{ role: "user", content: "test" }]
        };
        break;
        
      case 'moonshot':
        endpoint = 'https://api.moonshot.cn/v1/chat/completions';
        testPayload = {
          model: "moonshot-v1-8k",
          messages: [{ role: "user", content: "test" }],
          max_tokens: 1
        };
        break;
        
      case 'gemini':
        endpoint = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
        testPayload = {
          contents: [{ parts: [{ text: "test" }] }]
        };
        break;
        
      case 'deepseek':
        endpoint = 'https://api.deepseek.com/v1/chat/completions';
        testPayload = {
          model: "deepseek-chat",
          messages: [{ role: "user", content: "test" }],
          max_tokens: 1
        };
        break;
        
      default:
        throw new Error('Unsupported provider');
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // 根据不同提供商设置认证头
    switch (providerId) {
      case 'openai':
        headers['Authorization'] = `Bearer ${key}`;
        break;
      case 'claude':
        headers['x-api-key'] = key;
        headers['anthropic-version'] = '2023-06-01';
        break;
      case 'moonshot':
        headers['Authorization'] = `Bearer ${key}`;
        break;
      case 'gemini':
        endpoint = `${endpoint}?key=${key}`;
        break;
      case 'deepseek':
        headers['Authorization'] = `Bearer ${key}`;
        break;
    }

    const response = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(testPayload)
    });

    const data = await response.json();
    
    // 检查响应是否包含错误
    if (response.status !== 200) {
      throw new Error(data.error?.message || 'Invalid API key');
    }

    // API Key 验证成功
    validatedKeys[providerId] = true;
    
    // 保存验证成功的 key
    const storage = await chrome.storage.sync.get([STORAGE_KEYS.API_KEYS, STORAGE_KEYS.SETTINGS])
    const keys = storage[STORAGE_KEYS.API_KEYS] || {}
    const settings = storage[STORAGE_KEYS.SETTINGS] || {}

    keys[providerId] = key;

    // 如果是第一个验证成功的 key，设置为默认提供商
    if (Object.keys(validatedKeys).length === 1) {
      settings.defaultProvider = providerId;
      updateModelOptions(false);
    }

    await chrome.storage.sync.set({
      [STORAGE_KEYS.API_KEYS]: keys,
      [STORAGE_KEYS.VALIDATED_KEYS]: validatedKeys,
      [STORAGE_KEYS.SETTINGS]: settings
    });

    showNotification(`${providers.value.find(p => p.id === providerId)?.name || providerId} API key validated successfully`, 'success');
  } catch (err) {
    validatedKeys[providerId] = false;
    const errorMessage = err instanceof Error ? err.message : String(err);
    showNotification(`${providers.value.find(p => p.id === providerId)?.name || providerId}: ${errorMessage}`, 'error');
  } finally {
    validating[providerId] = false;
  }
}

// 修改防抖时间，使响应更快但不会太频繁
const debouncedValidateApiKey = debounce(validateApiKey, 800)

const toggleVisibility = (providerId: string) => {
  showPassword[providerId] = !showPassword[providerId]
  const input = document.getElementById(`${providerId}-key`) as HTMLInputElement
  if (input) {
    input.type = showPassword[providerId] ? 'text' : 'password'
  }
}

const updateModelOptions = (showNotificationMessage = true) => {
  const provider = settings.defaultProvider
  // 获取已验证的API keys
  chrome.storage.sync.get([STORAGE_KEYS.VALIDATED_KEYS], (storage) => {
    const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {}
    
    // 更新provider选项的禁用状态
    providers.value.forEach(p => {
      const option = document.querySelector(`option[value="${p.id}"]`) as HTMLOptionElement
      if (option) {
        option.disabled = !validatedKeys[p.id]
      }
    })

    // 只有当服务商已验证时才显示其模型列表
    if (validatedKeys[provider]) {
      availableModels.value = PROVIDER_MODELS[provider as keyof typeof PROVIDER_MODELS] || []
      if (!availableModels.value.find(m => m.id === settings.defaultModel)) {
        settings.defaultModel = availableModels.value[0]?.id || ''
      }
    } else {
      availableModels.value = []
    }
    saveSettings(showNotificationMessage)
  })
}

// 修改 saveSettings 函数
const saveSettings = async (showNotificationMessage = true) => {
  try {
    await chrome.storage.sync.set({ [STORAGE_KEYS.SETTINGS]: settings })
    if (showNotificationMessage) {
      showNotification('Settings saved successfully', 'success')
    }
  } catch (err) {
    const message = handleError(err, 'Failed to save settings')
    showNotification(message, 'error')
  }
}

const openProjectModal = () => {
  showProjectModal.value = true
}

const showNotification = (message: string, type: 'success' | 'error') => {
  // 清除之前的定时器
  if (notification.timer) {
    clearTimeout(notification.timer)
  }

  // 更新通知状态
  Object.assign(notification, {
    show: true,
    message,
    type,
    timer: setTimeout(() => {
      hideNotification()
    }, 3000)
  })
}

const hideNotification = () => {
  notification.show = false
  if (notification.timer) {
    clearTimeout(notification.timer)
    notification.timer = undefined
  }
}

// 项目相关方法
const editProject = (project: Project) => {
  editingProject.value = project
  Object.assign(projectForm, project)
  showProjectModal.value = true
}

const closeProjectModal = () => {
  showProjectModal.value = false
  editingProject.value = null
  Object.assign(projectForm, {
    name: '',
    description: '',
    environment: '',
    template: ''
  })
}

const saveProject = async () => {
  try {
    const storage = await chrome.storage.sync.get([STORAGE_KEYS.PROJECTS])
    const currentProjects = storage[STORAGE_KEYS.PROJECTS] || []
    
    if (editingProject.value) {
      // 更新现有项目
      const index = currentProjects.findIndex((p: Project) => p.id === editingProject.value?.id)
      if (index !== -1) {
        currentProjects[index] = {
          ...editingProject.value,
          ...projectForm
        }
      }
    } else {
      // 添加新项目
      currentProjects.push({
        id: crypto.randomUUID(),
        ...projectForm
      })
    }
    
    await chrome.storage.sync.set({ [STORAGE_KEYS.PROJECTS]: currentProjects })
    projects.value = currentProjects
    showNotification(
      `Project ${editingProject.value ? 'updated' : 'added'} successfully`, 
      'success'
    )
    closeProjectModal()
  } catch (error) {
    showNotification(
      `Failed to ${editingProject.value ? 'update' : 'add'} project`, 
      'error'
    )
  }
}

const deleteProject = async (projectId: string) => {
  if (!confirm('Are you sure you want to delete this project?')) {
    return
  }

  try {
    const updatedProjects = projects.value.filter(p => p.id !== projectId)
    await chrome.storage.sync.set({ [STORAGE_KEYS.PROJECTS]: updatedProjects })
    projects.value = updatedProjects
    showNotification('Project deleted successfully', 'success')
  } catch (error) {
    showNotification('Failed to delete project', 'error')
  }
}

// 修改 debouncedSaveSettings 中的错误处理
const debouncedSaveSettings = debounce(async (newSettings: Settings) => {
  try {
    await chrome.storage.sync.set({ [STORAGE_KEYS.SETTINGS]: newSettings })
  } catch (err) {
    const message = handleError(err, '保存设置失败')
    showNotification(message, 'error')
  }
}, 500)

// 监听设置变化
watch(settings, (newSettings) => {
  debouncedSaveSettings(newSettings)
}, { deep: true })

// 修改消息监听器处理逻辑
chrome.runtime.onMessage.addListener(async (message) => {
  if (message.type === 'apiKeyValidationResult') {
    const { provider, result } = message
    
    if (result.success) {
      // API Key 验证成功
      validatedKeys[provider] = true
      
      // 保存验证成功的 key
      const storage = await chrome.storage.sync.get([STORAGE_KEYS.API_KEYS, STORAGE_KEYS.SETTINGS])
      const keys = storage[STORAGE_KEYS.API_KEYS] || {}
      const settings = storage[STORAGE_KEYS.SETTINGS] || {}

      keys[provider] = apiKeys[provider]

      // 如果是第一个验证成功的 key，设置为默认提供商
      if (Object.keys(validatedKeys).length === 1) {
        settings.defaultProvider = provider
        updateModelOptions(false)
      }

      await chrome.storage.sync.set({
        [STORAGE_KEYS.API_KEYS]: keys,
        [STORAGE_KEYS.VALIDATED_KEYS]: validatedKeys,
        [STORAGE_KEYS.SETTINGS]: settings
      })

      showNotification(`${providers.value.find(p => p.id === provider)?.name || provider} API key validated successfully`, 'success')
    } else {
      // API Key 验证失败
      validatedKeys[provider] = false
      showNotification(result.error || `${providers.value.find(p => p.id === provider)?.name || provider} API key is invalid`, 'error')
    }
    validating[provider] = false
  }
})

// 初始化
onMounted(async () => {
  isLoading.value = true
  try {
    const storage = await chrome.storage.sync.get([
      STORAGE_KEYS.API_KEYS,
      STORAGE_KEYS.SETTINGS,
      STORAGE_KEYS.VALIDATED_KEYS,
      STORAGE_KEYS.TOKEN_STATS,
      STORAGE_KEYS.PROJECTS,
      STORAGE_KEYS.ACTIVE_TAB
    ])

    // 初始化 API keys
    Object.assign(apiKeys, storage[STORAGE_KEYS.API_KEYS] || {})
    Object.assign(validatedKeys, storage[STORAGE_KEYS.VALIDATED_KEYS] || {})
    
    // 初始化设置
    Object.assign(settings, storage[STORAGE_KEYS.SETTINGS] || {})
    
    // 初始化 token 统计
    Object.assign(tokenStats, storage[STORAGE_KEYS.TOKEN_STATS] || {})
    
    // 初始化项目列表
    projects.value = storage[STORAGE_KEYS.PROJECTS] || []
    
    // 初始化活动标签页
    const savedTab = storage[STORAGE_KEYS.ACTIVE_TAB]
    if (savedTab) {
      activeTab.value = savedTab
    }

    // 初始化可用模型，不显示通知
    updateModelOptions(false)
  } catch (error) {
    showNotification('Failed to initialize settings', 'error')
  } finally {
    isLoading.value = false
  }
})

// 在组件卸载时清理
onUnmounted(() => {
  if (notification.timer) {
    clearTimeout(notification.timer)
  }
})
</script>

<style>
/* CSS 已经在 settings.css 中定义 */
</style>

<style scoped>
/* 修改 loading spinner 样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* API Key 输入框中的小型 loading spinner */
.key-input-group {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.key-input {
  flex: 1;
  padding-right: 70px;
  transition: all 0.3s ease;
}

.key-input.validating {
  border-color: var(--warning-color);
  background-color: rgba(217, 119, 6, 0.05);
  box-shadow: 0 0 0 1px var(--warning-color);
}

.key-input.valid {
  border-color: var(--success-color);
  background-color: rgba(22, 163, 74, 0.05);
  box-shadow: 0 0 0 1px var(--success-color);
}

.key-input.invalid {
  border-color: var(--danger-color);
  background-color: rgba(220, 38, 38, 0.05);
  box-shadow: 0 0 0 1px var(--danger-color);
}

.loading-spinner {
  position: absolute;
  right: 40px;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.loading-spinner.active {
  opacity: 1;
  border-color: var(--border-color);
  border-top-color: var(--primary-color);
  animation: spin 0.8s linear infinite;
}

.visibility-toggle {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.visibility-toggle:hover {
  opacity: 1;
}

.eye-icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
  transition: transform 0.2s ease;
}

.visibility-toggle:hover .eye-icon {
  transform: scale(1.1);
}

.visibility-toggle:hover .eye-icon.show-password {
  display: block;
}

.visibility-toggle:hover .eye-icon.hide-password {
  display: none;
}

/* 加载动画样式 */
.loading-spinner.active {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
</style>