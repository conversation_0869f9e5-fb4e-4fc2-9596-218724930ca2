{"name": "wxt-vue-starter", "description": "manifest.json description", "private": true, "version": "1.0.6", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "vue-tsc --noEmit", "postinstall": "wxt prepare", "clean": "rimraf .output"}, "dependencies": {"ai": "^4.3.16", "build": "^0.1.4", "canvas-confetti": "^1.9.3", "vue": "^3.5.12"}, "devDependencies": {"@types/chrome": "^0.0.280", "@wxt-dev/module-vue": "^1.0.1", "typescript": "5.6.3", "vue-tsc": "^2.1.10", "wxt": "^0.19.28"}}